import NextAuth from "next-auth";
import Credentials from "next-auth/providers/credentials";
import Discord from "next-auth/providers/discord";
import { authConfig } from "./auth.config";
import { prisma } from "./lib/prisma";
import { verifyPassword } from "./utils/auth";
import { supabase } from "./lib/supabase";

// Custom logger to control console output
const logger = {
  error(error: Error) {
    // Only log certain errors or in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`[auth][error]:`, error);
    }
  },
  warn(code: string) {
    if (process.env.NODE_ENV === 'development') {
      console.warn(`[auth][warn] ${code}`);
    }
  },
  debug(code: string, metadata?: unknown) {
    if (process.env.NODE_ENV === 'development' && process.env.AUTH_DEBUG === 'true') {
      console.log(`[auth][debug] ${code}:`, metadata);
    }
  }
};

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut
} = NextAuth({
  logger,
  ...authConfig,
  providers: [
    Discord({
      clientId: process.env.AUTH_DISCORD_ID!,
      clientSecret: process.env.AUTH_DISCORD_SECRET!,
    }),
    Credentials({
      async authorize(credentials) {
        try {
          const { username, password } = credentials as {
            username: string;
            password: string;
          };

          if (!username || !password) {
            return null;
          }

          const user = await prisma.adminUser.findUnique({
            where: { username },
          });

          if (!user) {
            // User not found - don't expose this information
            return null;
          }

          // Use our bcrypt password verification
          const passwordMatch = await verifyPassword(password, user.password!);

          if (!passwordMatch) {
            // Password doesn't match - don't expose this information
            return null;
          }

          return {
            id: user.id,
            name: user.username,
            email: user.email,
          };
        } catch (error) {
          // Log the error but don't expose it to the client
          if (process.env.NODE_ENV === 'development') {
            console.error('Authentication error:', error);
          }
          return null;
        }
      },
    }),
  ],
  session: { strategy: "jwt" },
  callbacks: {
    ...authConfig.callbacks,
    async signIn({ user, account, profile }) {
      if (account?.provider === "discord") {
        try {
          // Check if user already exists
          const existingUser = await prisma.adminUser.findUnique({
            where: {
              provider_providerId: {
                provider: "discord",
                providerId: user.id,
              },
            },
          });

          if (!existingUser) {
            // Create new user for Discord OAuth
            await prisma.adminUser.create({
              data: {
                name: user.name || profile?.username || "Discord User",
                email: user.email,
                image: user.image,
                provider: "discord",
                providerId: user.id,
              },
            });
          }
          return true;
        } catch (error) {
          console.error("Error during Discord sign in:", error);
          return false;
        }
      }
      return true;
    },
    jwt: async ({ token, user, account }) => {
      if (user) {
        token.id = user.id;
        token.name = user.name;
        token.email = user.email;
        token.image = user.image;

        if (account?.provider === "discord") {
          token.provider = "discord";
        } else {
          token.provider = "credentials";
        }
      }
      return token;
    },
    session: async ({ session, token }) => {
      if (token) {
        session.user.id = token.id as string;
        session.user.name = token.name as string;
        session.user.email = token.email as string;
        session.user.image = token.image as string;
      }
      return session;
    },
  },
});
