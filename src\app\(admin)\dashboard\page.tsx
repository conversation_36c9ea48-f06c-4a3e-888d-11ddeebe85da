import { auth } from '@/auth';

export default async function DashboardPage() {
  const session = await auth();

  return (
    <div>
      <h1 className="text-3xl font-bold mb-8 text-white">Dashboard</h1>

      <div className="bg-[#343450] rounded-lg shadow-lg p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4 text-white">Welcome, {session?.user?.name || 'Admin'}</h2>
        <p className="text-gray-300">
          This is the admin dashboard for Taroo manga reader. Use the sidebar to navigate to different sections.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-[#343450] border-l-4 border-primary rounded-lg shadow-lg p-6 transition-transform hover:-translate-y-1 duration-200">
          <h3 className="text-lg font-semibold mb-2 text-white">Manga Series</h3>
          <p className="text-gray-300 mb-4">Manage your manga series collection</p>
          <a href="/dashboard/series" className="text-primary hover:text-primary/80 flex items-center">
            View Series
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </a>
        </div>

        <div className="bg-[#343450] border-l-4 border-[#6e6bff] rounded-lg shadow-lg p-6 transition-transform hover:-translate-y-1 duration-200">
          <h3 className="text-lg font-semibold mb-2 text-white">Chapters</h3>
          <p className="text-gray-300 mb-4">Manage chapters and uploads</p>
          <a href="/dashboard/chapters" className="text-[#6e6bff] hover:text-[#6e6bff]/80 flex items-center">
            View Chapters
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </a>
        </div>

        <div className="bg-[#343450] border-l-4 border-[#8a7bff] rounded-lg shadow-lg p-6 transition-transform hover:-translate-y-1 duration-200">
          <h3 className="text-lg font-semibold mb-2 text-white">Statistics</h3>
          <p className="text-gray-300 mb-4">View site statistics and analytics</p>
          <a href="/dashboard/stats" className="text-[#8a7bff] hover:text-[#8a7bff]/80 flex items-center">
            View Stats
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </a>
        </div>
      </div>
    </div>
  );
}
